{% extends 'base.html' %}

{% block title %}المستخدمين{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col">
            <h2>المستخدمين</h2>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('register') }}" class="btn btn-success">
                <i class="bi bi-person-plus"></i> إضافة مستخدم جديد
            </a>
        </div>
    </div>

    <div class="card shadow">
        <div class="card-header bg-dark text-white">
            <h5 class="mb-0">قائمة المستخدمين</h5>
        </div>
        <div class="card-body">
            {% if users %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>اسم المستخدم</th>
                            <th>عدد المهام</th>
                            {% if session.username == 'admin' %}
                            <th>الإجراءات</th>
                            {% endif %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>{{ user['id'] }}</td>
                            <td>
                                <strong>{{ user['username'] }}</strong>
                                <br>
                                <small class="text-muted">انضم في: {{ user['created_at'][:10] }}</small>
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ user['tasks_count'] }} مهام</span>
                            </td>
                            {% if session.username == 'admin' %}
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-info" title="عرض المهام">
                                        <i class="bi bi-list-task"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary" title="تعديل">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                </div>
                            </td>
                            {% endif %}
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info">
                لا يوجد مستخدمين في النظام حاليًا.
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}