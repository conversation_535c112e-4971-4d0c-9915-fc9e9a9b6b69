{% extends 'base.html' %}

{% block title %}{% if task %}تعديل مهمة{% else %}إضافة مهمة جديدة{% endif %}{% endblock %}

{% block content %}
<div class="d-flex justify-content-center align-items-center" style="min-height: 80vh;">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header bg-dark text-white">
                <h4 class="mb-0">{% if task %}تعديل مهمة{% else %}إضافة مهمة جديدة{% endif %}</h4>
            </div>
            <div class="card-body">
                <form method="post" id="task-form">
                    <div class="mb-3">
                        <label for="title" class="form-label">عنوان المهمة</label>
                        <input type="text" class="form-control" id="title" name="title"
                            value="{{ task['title'] if task else '' }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف المهمة</label>
                        <textarea class="form-control" id="description" name="description"
                            rows="3">{{ task['description'] if task else '' }}</textarea>
                    </div>
                    <div class="mb-3">
                        <label for="due_date" class="form-label">تاريخ التسليم</label>
                        <input type="date" class="form-control" id="due_date" name="due_date"
                            value="{% if task %}{{ task['due_date'][:10] if task['due_date'] else '' }}{% endif %}"
                            required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">حالة المهمة</label>
                        <div class="d-flex">
                            <div class="form-check me-3">
                                <input class="form-check-input" type="radio" name="status" id="status-ongoing"
                                    value="جاري" {% if not task or task['status']=='جاري' %}checked{% endif %}>
                                <label class="form-check-label" for="status-ongoing">جاري</label>
                            </div>
                            <div class="form-check me-3">
                                <input class="form-check-input" type="radio" name="status" id="status-completed"
                                    value="مكتمل" {% if task and task['status']=='مكتمل' %}checked{% endif %}>
                                <label class="form-check-label" for="status-completed">مكتمل</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="status" id="status-late"
                                    value="متأخر" {% if task and task['status']=='متأخر' %}checked{% endif %}>
                                <label class="form-check-label" for="status-late">متأخر</label>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('dashboard') }}" class="btn btn-danger">إلغاء</a>
                        <button type="submit" class="btn btn-dark">
                            {% if task %}حفظ التغييرات{% else %}إضافة المهمة{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function () {
        $('#task-form').submit(function (e) {
            // يمكن إضافة التحقق من صحة النموذج هنا قبل الإرسال
            // مثال: التحقق من أن تاريخ التسليم ليس في الماضي

            // إذا كنت ترغب في استخدام AJAX لإرسال النموذج بدلاً من الإرسال التقليدي
            // e.preventDefault();
            // const formData = $(this).serialize();
            // $.ajax({
            //     url: $(this).attr('action') || window.location.href,
            //     method: 'POST',
            //     data: formData,
            //     success: function(response) {
            //         if (response.success) {
            //             window.location.href = '/dashboard';
            //         }
            //     }
            // });
        });
    });
</script>
{% endblock %}